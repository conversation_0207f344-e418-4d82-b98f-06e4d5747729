import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import { router, useLocalSearchParams } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import React, { useEffect, useRef, useState } from 'react';
import { Dimensions, ScrollView, StyleSheet, TouchableOpacity } from 'react-native';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

export default function TeleprompterScreen() {
  const params = useLocalSearchParams<{ text: string; title: string }>();
  const colorScheme = useColorScheme();
  const scrollViewRef = useRef<ScrollView>(null);
  
  const [isPlaying, setIsPlaying] = useState(false);
  const [speed, setSpeed] = useState(2); // 1-5 速度等级
  const [fontSize, setFontSize] = useState(24);
  const [isSettingsVisible, setIsSettingsVisible] = useState(false);
  const [scrollY, setScrollY] = useState(0);
  
  const scrollIntervalRef = useRef<ReturnType<typeof setInterval> | null>(null);
  
  // 自动滚动逻辑
  useEffect(() => {
    if (isPlaying) {
      scrollIntervalRef.current = setInterval(() => {
        setScrollY(prev => {
          const newY = prev + speed;
          scrollViewRef.current?.scrollTo({ y: newY, animated: false });
          return newY;
        });
      }, 50); // 每50ms滚动一次
    } else {
      if (scrollIntervalRef.current) {
        clearInterval(scrollIntervalRef.current);
        scrollIntervalRef.current = null;
      }
    }
    
    return () => {
      if (scrollIntervalRef.current) {
        clearInterval(scrollIntervalRef.current);
      }
    };
  }, [isPlaying, speed]);
  
  const handlePlayPause = () => {
    setIsPlaying(!isPlaying);
  };
  
  const handleSpeedChange = (newSpeed: number) => {
    setSpeed(newSpeed);
  };
  
  const handleFontSizeChange = (newSize: number) => {
    setFontSize(Math.max(16, Math.min(40, newSize)));
  };
  
  const handleReset = () => {
    setIsPlaying(false);
    setScrollY(0);
    scrollViewRef.current?.scrollTo({ y: 0, animated: true });
  };
  
  const toggleSettings = () => {
    setIsSettingsVisible(!isSettingsVisible);
  };

  return (
    <>
      <StatusBar style={colorScheme === 'dark' ? 'light' : 'dark'} />
      <ThemedView style={styles.container}>
        {/* 标题栏 */}
        <ThemedView style={styles.header}>
          <TouchableOpacity onPress={() => router.back()}>
            <IconSymbol name="chevron.left" size={24} color={Colors[colorScheme ?? 'light'].text} />
          </TouchableOpacity>
          <ThemedText type="subtitle" style={styles.headerTitle}>
            {params.title}
          </ThemedText>
          <TouchableOpacity onPress={toggleSettings}>
            <IconSymbol name="gear" size={24} color={Colors[colorScheme ?? 'light'].text} />
          </TouchableOpacity>
        </ThemedView>

        {/* 设置面板 */}
        {isSettingsVisible && (
          <ThemedView style={[styles.settingsPanel, { backgroundColor: Colors[colorScheme ?? 'light'].background }]}>
            <ThemedView style={styles.settingRow}>
              <ThemedText style={styles.settingLabel}>滚动速度</ThemedText>
              <ThemedView style={styles.speedControls}>
                {[1, 2, 3, 4, 5].map((level) => (
                  <TouchableOpacity
                    key={level}
                    style={[
                      styles.speedButton,
                      speed === level && { backgroundColor: Colors[colorScheme ?? 'light'].tint }
                    ]}
                    onPress={() => handleSpeedChange(level)}
                  >
                    <ThemedText style={[
                      styles.speedButtonText,
                      speed === level && { color: 'white' }
                    ]}>
                      {level}
                    </ThemedText>
                  </TouchableOpacity>
                ))}
              </ThemedView>
            </ThemedView>
            
            <ThemedView style={styles.settingRow}>
              <ThemedText style={styles.settingLabel}>字体大小</ThemedText>
              <ThemedView style={styles.fontControls}>
                <TouchableOpacity
                  style={styles.fontButton}
                  onPress={() => handleFontSizeChange(fontSize - 2)}
                >
                  <IconSymbol name="minus" size={20} color={Colors[colorScheme ?? 'light'].text} />
                </TouchableOpacity>
                <ThemedText style={styles.fontSizeText}>{fontSize}</ThemedText>
                <TouchableOpacity
                  style={styles.fontButton}
                  onPress={() => handleFontSizeChange(fontSize + 2)}
                >
                  <IconSymbol name="plus" size={20} color={Colors[colorScheme ?? 'light'].text} />
                </TouchableOpacity>
              </ThemedView>
            </ThemedView>
          </ThemedView>
        )}

        {/* 文本滚动区域 */}
        <ScrollView
          ref={scrollViewRef}
          style={styles.scrollView}
          showsVerticalScrollIndicator={false}
          scrollEnabled={!isPlaying} // 播放时禁用手动滚动
        >
          <ThemedView style={styles.textContainer}>
            <ThemedText style={[styles.scriptText, { fontSize }]}>
              {params.text}
            </ThemedText>
            {/* 底部留白，确保文本能滚动到最后 */}
            <ThemedView style={{ height: screenHeight * 0.8 }} />
          </ThemedView>
        </ScrollView>

        {/* 控制按钮 */}
        <ThemedView style={styles.controls}>
          <TouchableOpacity
            style={[styles.controlButton, styles.resetButton]}
            onPress={handleReset}
          >
            <IconSymbol name="arrow.clockwise" size={24} color={Colors[colorScheme ?? 'light'].text} />
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.controlButton, styles.playButton, { backgroundColor: Colors[colorScheme ?? 'light'].tint }]}
            onPress={handlePlayPause}
          >
            <IconSymbol 
              name={isPlaying ? "pause.fill" : "play.fill"} 
              size={32} 
              color="white" 
            />
          </TouchableOpacity>
          
                     <TouchableOpacity
             style={[styles.controlButton, styles.resetButton]}
             onPress={() => router.push({
               pathname: '/recording',
               params: { 
                 text: params.text,
                 title: params.title
               }
             })}
           >
             <IconSymbol name="video.fill" size={24} color={Colors[colorScheme ?? 'light'].text} />
           </TouchableOpacity>
        </ThemedView>
      </ThemedView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 50,
    paddingBottom: 15,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  headerTitle: {
    flex: 1,
    textAlign: 'center',
    fontSize: 18,
    fontWeight: '600',
  },
  settingsPanel: {
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  settingRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 15,
  },
  settingLabel: {
    fontSize: 16,
    fontWeight: '500',
  },
  speedControls: {
    flexDirection: 'row',
    gap: 8,
  },
  speedButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.2)',
  },
  speedButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  fontControls: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 15,
  },
  fontButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.2)',
  },
  fontSizeText: {
    fontSize: 16,
    fontWeight: '600',
    minWidth: 30,
    textAlign: 'center',
  },
  scrollView: {
    flex: 1,
  },
  textContainer: {
    padding: 20,
    paddingTop: 40,
  },
  scriptText: {
    lineHeight: 1.8,
    textAlign: 'center',
  },
  controls: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 20,
    paddingHorizontal: 20,
    gap: 20,
  },
  controlButton: {
    width: 60,
    height: 60,
    borderRadius: 30,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.2)',
  },
  playButton: {
    width: 80,
    height: 80,
    borderRadius: 40,
    borderWidth: 0,
  },
  resetButton: {
    backgroundColor: 'transparent',
  },
}); 