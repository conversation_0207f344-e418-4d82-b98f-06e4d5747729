import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import React from 'react';
import { Linking, ScrollView, StyleSheet, TouchableOpacity } from 'react-native';

export default function SettingsScreen() {
  const colorScheme = useColorScheme();

  const handleOpenGitHub = () => {
    Linking.openURL('https://github.com');
  };

  const handleRateApp = () => {
    // 这里可以添加应用商店评分逻辑
    console.log('Rate app');
  };

  const handleContact = () => {
    Linking.openURL('mailto:<EMAIL>');
  };

  return (
    <ScrollView style={styles.container}>
      <ThemedView style={styles.header}>
        <ThemedText type="title" style={styles.title}>设置</ThemedText>
        <ThemedText style={styles.subtitle}>提词器应用设置与信息</ThemedText>
      </ThemedView>

      <ThemedView style={styles.section}>
        <ThemedText type="subtitle" style={styles.sectionTitle}>关于应用</ThemedText>
        
        <ThemedView style={styles.infoCard}>
          <ThemedText style={styles.cardTitle}>📹 专业提词器</ThemedText>
          <ThemedText style={styles.cardDescription}>
            为视频创作者量身打造的专业提词器应用，支持文本滚动、速度调节、字体调整等功能。
            无论是录制短视频、直播演讲还是新闻播报，都能帮助您更流畅地表达。
          </ThemedText>
        </ThemedView>

        <ThemedView style={styles.featureList}>
          <ThemedText type="subtitle" style={styles.featuresTitle}>主要功能</ThemedText>
          
          <ThemedView style={styles.featureItem}>
            <IconSymbol name="play.fill" size={20} color={Colors[colorScheme ?? 'light'].tint} />
            <ThemedText style={styles.featureText}>智能文本滚动，多种速度可调</ThemedText>
          </ThemedView>
          
          <ThemedView style={styles.featureItem}>
            <IconSymbol name="textformat.size" size={20} color={Colors[colorScheme ?? 'light'].tint} />
            <ThemedText style={styles.featureText}>字体大小自由调节，适应不同距离</ThemedText>
          </ThemedView>
          
          <ThemedView style={styles.featureItem}>
            <IconSymbol name="video.fill" size={20} color={Colors[colorScheme ?? 'light'].tint} />
            <ThemedText style={styles.featureText}>边录边提词，一体化视频制作</ThemedText>
          </ThemedView>
          
          <ThemedView style={styles.featureItem}>
            <IconSymbol name="camera.rotate" size={20} color={Colors[colorScheme ?? 'light'].tint} />
            <ThemedText style={styles.featureText}>前后摄像头切换，灵活拍摄角度</ThemedText>
          </ThemedView>
          
          <ThemedView style={styles.featureItem}>
            <IconSymbol name="slider.horizontal.3" size={20} color={Colors[colorScheme ?? 'light'].tint} />
            <ThemedText style={styles.featureText}>提词器透明度调节，不遮挡拍摄</ThemedText>
          </ThemedView>
        </ThemedView>
      </ThemedView>

      <ThemedView style={styles.section}>
        <ThemedText type="subtitle" style={styles.sectionTitle}>使用技巧</ThemedText>
        
        <ThemedView style={styles.tipCard}>
          <ThemedText style={styles.tipTitle}>💡 使用建议</ThemedText>
          <ThemedText style={styles.tipText}>
            • 录制前先在提词器模式下练习几遍，熟悉文本内容
          </ThemedText>
          <ThemedText style={styles.tipText}>
            • 调整合适的滚动速度，保持自然的语速
          </ThemedText>
          <ThemedText style={styles.tipText}>
            • 使用前置摄像头时，可以看到自己的表情，更好地控制镜头感
          </ThemedText>
          <ThemedText style={styles.tipText}>
            • 长文本建议分段录制，便于后期剪辑
          </ThemedText>
        </ThemedView>
      </ThemedView>

      <ThemedView style={styles.section}>
        <ThemedText type="subtitle" style={styles.sectionTitle}>支持与反馈</ThemedText>
        
        <TouchableOpacity 
          style={[styles.actionButton, { borderColor: Colors[colorScheme ?? 'light'].tint }]}
          onPress={handleRateApp}
        >
          <IconSymbol name="star.fill" size={20} color={Colors[colorScheme ?? 'light'].tint} />
          <ThemedText style={[styles.actionButtonText, { color: Colors[colorScheme ?? 'light'].tint }]}>
            给应用评分
          </ThemedText>
        </TouchableOpacity>

        <TouchableOpacity 
          style={[styles.actionButton, { borderColor: Colors[colorScheme ?? 'light'].tint }]}
          onPress={handleContact}
        >
          <IconSymbol name="envelope.fill" size={20} color={Colors[colorScheme ?? 'light'].tint} />
          <ThemedText style={[styles.actionButtonText, { color: Colors[colorScheme ?? 'light'].tint }]}>
            联系我们
          </ThemedText>
        </TouchableOpacity>

        <TouchableOpacity 
          style={[styles.actionButton, { borderColor: Colors[colorScheme ?? 'light'].tint }]}
          onPress={handleOpenGitHub}
        >
          <IconSymbol name="link" size={20} color={Colors[colorScheme ?? 'light'].tint} />
          <ThemedText style={[styles.actionButtonText, { color: Colors[colorScheme ?? 'light'].tint }]}>
            开源项目
          </ThemedText>
        </TouchableOpacity>
      </ThemedView>

      <ThemedView style={styles.footer}>
        <ThemedText style={styles.version}>版本 1.0.0</ThemedText>
        <ThemedText style={styles.copyright}>© 2024 提词器应用</ThemedText>
      </ThemedView>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
  },
  header: {
    alignItems: 'center',
    marginTop: 40,
    marginBottom: 30,
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    opacity: 0.7,
    textAlign: 'center',
  },
  section: {
    marginBottom: 30,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 15,
  },
  infoCard: {
    padding: 20,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.1)',
    marginBottom: 20,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 8,
  },
  cardDescription: {
    fontSize: 15,
    lineHeight: 22,
    opacity: 0.8,
  },
  featureList: {
    marginBottom: 10,
  },
  featuresTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 15,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    gap: 12,
  },
  featureText: {
    fontSize: 15,
    flex: 1,
  },
  tipCard: {
    padding: 20,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.1)',
  },
  tipTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  tipText: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 6,
    opacity: 0.8,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 15,
    borderRadius: 10,
    borderWidth: 1,
    marginBottom: 10,
    gap: 8,
  },
  actionButtonText: {
    fontSize: 16,
    fontWeight: '500',
  },
  footer: {
    alignItems: 'center',
    marginTop: 20,
    marginBottom: 40,
  },
  version: {
    fontSize: 14,
    opacity: 0.6,
    marginBottom: 4,
  },
  copyright: {
    fontSize: 12,
    opacity: 0.5,
  },
});
