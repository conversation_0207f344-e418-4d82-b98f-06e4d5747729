import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import { router } from 'expo-router';
import React, { useEffect, useState } from 'react';
import { Alert, Keyboard, KeyboardAvoidingView, Platform, ScrollView, StyleSheet, TextInput, TouchableOpacity, TouchableWithoutFeedback } from 'react-native';

export default function HomeScreen() {
  const [scriptText, setScriptText] = useState('');
  const [scriptTitle, setScriptTitle] = useState('');
  const [isKeyboardVisible, setKeyboardVisible] = useState(false);
  const colorScheme = useColorScheme();

  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener(
      'keyboardDidShow',
      () => {
        setKeyboardVisible(true);
      },
    );
    const keyboardDidHideListener = Keyboard.addListener(
      'keyboardDidHide',
      () => {
        setKeyboardVisible(false);
      },
    );

    return () => {
      keyboardDidHideListener?.remove();
      keyboardDidShowListener?.remove();
    };
  }, []);

  const dismissKeyboard = () => {
    Keyboard.dismiss();
  };

  const handleStartTeleprompter = () => {
    if (!scriptText.trim()) {
      Alert.alert('提示', '请先输入要提词的文本内容');
      return;
    }
    
    // 传递文本内容到提词器页面
    router.push({
      pathname: '/teleprompter',
      params: { 
        text: scriptText,
        title: scriptTitle || '无标题文稿'
      }
    });
  };

  const handleStartRecording = () => {
    if (!scriptText.trim()) {
      Alert.alert('提示', '请先输入要提词的文本内容');
      return;
    }
    
    // 传递文本内容到录制页面
    router.push({
      pathname: '/recording',
      params: { 
        text: scriptText,
        title: scriptTitle || '无标题文稿'
      }
    });
  };

  return (
    <KeyboardAvoidingView 
      style={styles.container} 
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 90 : 0}
    >
      <TouchableWithoutFeedback onPress={dismissKeyboard}>
        <ScrollView 
          style={styles.scrollContainer}
          contentContainerStyle={styles.scrollContent}
          keyboardShouldPersistTaps="handled"
        >
          <ThemedView style={styles.header}>
            <ThemedText type="title" style={styles.title}>提词器</ThemedText>
            <ThemedText style={styles.subtitle}>为您的视频拍摄提供专业提词服务</ThemedText>
          </ThemedView>

          <ThemedView style={styles.inputContainer}>
            <ThemedText type="subtitle" style={styles.label}>文稿标题</ThemedText>
            <TextInput
              style={[styles.titleInput, { 
                backgroundColor: Colors[colorScheme ?? 'light'].background,
                color: Colors[colorScheme ?? 'light'].text,
                borderColor: Colors[colorScheme ?? 'light'].tint
              }]}
              placeholder="输入文稿标题（可选）"
              placeholderTextColor={Colors[colorScheme ?? 'light'].tabIconDefault}
              value={scriptTitle}
              onChangeText={setScriptTitle}
              returnKeyType="next"
              onSubmitEditing={() => {
                // 可以在这里切换到下一个输入框
              }}
            />
            
            <ThemedText type="subtitle" style={styles.label}>文稿内容</ThemedText>
            <TextInput
              style={[styles.textInput, { 
                backgroundColor: Colors[colorScheme ?? 'light'].background,
                color: Colors[colorScheme ?? 'light'].text,
                borderColor: Colors[colorScheme ?? 'light'].tint,
                minHeight: isKeyboardVisible ? 120 : 200
              }]}
              placeholder="在此输入您的文稿内容..."
              placeholderTextColor={Colors[colorScheme ?? 'light'].tabIconDefault}
              value={scriptText}
              onChangeText={setScriptText}
              multiline
              textAlignVertical="top"
              blurOnSubmit={false}
              returnKeyType="done"
              onSubmitEditing={dismissKeyboard}
            />
          </ThemedView>

          <ThemedView style={[styles.buttonContainer, isKeyboardVisible && styles.buttonContainerKeyboard]}>
            <TouchableOpacity 
              style={[styles.button, styles.primaryButton, { backgroundColor: Colors[colorScheme ?? 'light'].tint }]}
              onPress={handleStartTeleprompter}
            >
              <IconSymbol name="play.fill" size={24} color="white" />
              <ThemedText style={[styles.buttonText, { color: 'white' }]}>开始提词</ThemedText>
            </TouchableOpacity>

            <TouchableOpacity 
              style={[styles.button, styles.secondaryButton, { borderColor: Colors[colorScheme ?? 'light'].tint }]}
              onPress={handleStartRecording}
            >
              <IconSymbol name="video.fill" size={24} color={Colors[colorScheme ?? 'light'].tint} />
              <ThemedText style={[styles.buttonText, { color: Colors[colorScheme ?? 'light'].tint }]}>
                边录边提词
              </ThemedText>
            </TouchableOpacity>
          </ThemedView>

          {/* 键盘显示时的隐藏键盘按钮 */}
          {isKeyboardVisible && (
            <TouchableOpacity 
              style={[styles.dismissButton, { backgroundColor: Colors[colorScheme ?? 'light'].tint }]}
              onPress={dismissKeyboard}
            >
              <ThemedText style={styles.dismissButtonText}>完成输入</ThemedText>
            </TouchableOpacity>
          )}
        </ScrollView>
      </TouchableWithoutFeedback>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContainer: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    padding: 20,
    paddingTop: 60,
  },
  header: {
    alignItems: 'center',
    marginBottom: 40,
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    opacity: 0.7,
    textAlign: 'center',
  },
  inputContainer: {
    flex: 1,
    marginBottom: 20,
  },
  label: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 8,
    marginTop: 16,
  },
  titleInput: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    marginBottom: 16,
  },
  textInput: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    minHeight: 200,
    maxHeight: 300,
  },
  buttonContainer: {
    gap: 12,
    paddingBottom: 20,
  },
  buttonContainerKeyboard: {
    marginTop: 20,
  },
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 12,
    gap: 8,
  },
  primaryButton: {
    // backgroundColor will be set dynamically
  },
  secondaryButton: {
    borderWidth: 2,
    backgroundColor: 'transparent',
  },
  buttonText: {
    fontSize: 18,
    fontWeight: '600',
  },
  dismissButton: {
    alignSelf: 'center',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 20,
    marginTop: 10,
    marginBottom: 20,
  },
  dismissButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
});
