import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import { CameraType, CameraView, useCameraPermissions } from 'expo-camera';
import { router, useLocalSearchParams } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import React, { useEffect, useRef, useState } from 'react';
import { Alert, Dimensions, StyleSheet, TouchableOpacity, View } from 'react-native';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

export default function RecordingScreen() {
  const params = useLocalSearchParams<{ text: string; title: string }>();
  const colorScheme = useColorScheme();
  const cameraRef = useRef<CameraView>(null);
  
  const [permission, requestPermission] = useCameraPermissions();
  const [facing, setFacing] = useState<CameraType>('front');
  const [isRecording, setIsRecording] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [speed, setSpeed] = useState(2);
  const [fontSize, setFontSize] = useState(20);
  const [scrollY, setScrollY] = useState(0);
  const [isSettingsVisible, setIsSettingsVisible] = useState(false);
  const [teleprompterOpacity, setTeleprompterOpacity] = useState(0.8);
  
  const scrollIntervalRef = useRef<ReturnType<typeof setInterval> | null>(null);
  const textContainerRef = useRef<View>(null);
  
  // 自动滚动逻辑
  useEffect(() => {
    if (isPlaying) {
      scrollIntervalRef.current = setInterval(() => {
        setScrollY(prev => prev + speed);
      }, 50);
    } else {
      if (scrollIntervalRef.current) {
        clearInterval(scrollIntervalRef.current);
        scrollIntervalRef.current = null;
      }
    }
    
    return () => {
      if (scrollIntervalRef.current) {
        clearInterval(scrollIntervalRef.current);
      }
    };
  }, [isPlaying, speed]);
  
  if (!permission) {
    return (
      <ThemedView style={styles.container}>
        <ThemedText>正在请求摄像头权限...</ThemedText>
      </ThemedView>
    );
  }

  if (!permission.granted) {
    return (
      <ThemedView style={styles.container}>
        <ThemedText style={styles.message}>需要摄像头权限才能录制视频</ThemedText>
        <TouchableOpacity style={styles.permissionButton} onPress={requestPermission}>
          <ThemedText style={styles.permissionButtonText}>授予权限</ThemedText>
        </TouchableOpacity>
      </ThemedView>
    );
  }

  const handleStartStopRecording = async () => {
    if (isRecording) {
      // 停止录制
      setIsRecording(false);
      setIsPlaying(false);
      if (cameraRef.current) {
        try {
          await cameraRef.current.stopRecording();
          Alert.alert('成功', '视频录制完成');
        } catch (error) {
          Alert.alert('错误', '停止录制失败');
        }
      }
    } else {
      // 开始录制
      if (cameraRef.current) {
                 try {
           setIsRecording(true);
           const video = await cameraRef.current.recordAsync();
           if (video) {
             console.log('视频已保存:', video.uri);
           }
         } catch (error) {
           setIsRecording(false);
           Alert.alert('错误', '录制失败');
         }
      }
    }
  };

  const handlePlayPauseTeleprompter = () => {
    setIsPlaying(!isPlaying);
  };

  const handleSpeedChange = (newSpeed: number) => {
    setSpeed(newSpeed);
  };

  const handleFontSizeChange = (newSize: number) => {
    setFontSize(Math.max(14, Math.min(28, newSize)));
  };

  const handleResetTeleprompter = () => {
    setIsPlaying(false);
    setScrollY(0);
  };

  const toggleCamera = () => {
    setFacing(current => (current === 'back' ? 'front' : 'back'));
  };

  const toggleSettings = () => {
    setIsSettingsVisible(!isSettingsVisible);
  };

  const handleOpacityChange = (newOpacity: number) => {
    setTeleprompterOpacity(Math.max(0.3, Math.min(1, newOpacity)));
  };

  return (
    <>
      <StatusBar style="light" />
      <View style={styles.container}>
        {/* 摄像头视图 */}
        <CameraView 
          style={styles.camera} 
          facing={facing}
          ref={cameraRef}
        >
          {/* 顶部控制栏 */}
          <ThemedView style={styles.topControls}>
            <TouchableOpacity onPress={() => router.back()}>
              <IconSymbol name="chevron.left" size={24} color="white" />
            </TouchableOpacity>
            <ThemedText style={styles.topTitle}>{params.title}</ThemedText>
            <TouchableOpacity onPress={toggleSettings}>
              <IconSymbol name="gear" size={24} color="white" />
            </TouchableOpacity>
          </ThemedView>

          {/* 设置面板 */}
          {isSettingsVisible && (
            <ThemedView style={styles.floatingSettings}>
              <ThemedView style={styles.settingRow}>
                <ThemedText style={styles.settingLabel}>滚动速度</ThemedText>
                <ThemedView style={styles.speedControls}>
                  {[1, 2, 3, 4, 5].map((level) => (
                    <TouchableOpacity
                      key={level}
                      style={[
                        styles.speedButton,
                        speed === level && { backgroundColor: Colors[colorScheme ?? 'light'].tint }
                      ]}
                      onPress={() => handleSpeedChange(level)}
                    >
                      <ThemedText style={[
                        styles.speedButtonText,
                        speed === level && { color: 'white' }
                      ]}>
                        {level}
                      </ThemedText>
                    </TouchableOpacity>
                  ))}
                </ThemedView>
              </ThemedView>
              
              <ThemedView style={styles.settingRow}>
                <ThemedText style={styles.settingLabel}>字体大小</ThemedText>
                <ThemedView style={styles.fontControls}>
                  <TouchableOpacity
                    style={styles.fontButton}
                    onPress={() => handleFontSizeChange(fontSize - 2)}
                  >
                    <IconSymbol name="minus" size={16} color={Colors[colorScheme ?? 'light'].text} />
                  </TouchableOpacity>
                  <ThemedText style={styles.fontSizeText}>{fontSize}</ThemedText>
                  <TouchableOpacity
                    style={styles.fontButton}
                    onPress={() => handleFontSizeChange(fontSize + 2)}
                  >
                    <IconSymbol name="plus" size={16} color={Colors[colorScheme ?? 'light'].text} />
                  </TouchableOpacity>
                </ThemedView>
              </ThemedView>

              <ThemedView style={styles.settingRow}>
                <ThemedText style={styles.settingLabel}>提词器透明度</ThemedText>
                <ThemedView style={styles.opacityControls}>
                  <TouchableOpacity
                    style={styles.fontButton}
                    onPress={() => handleOpacityChange(teleprompterOpacity - 0.1)}
                  >
                    <IconSymbol name="minus" size={16} color={Colors[colorScheme ?? 'light'].text} />
                  </TouchableOpacity>
                  <ThemedText style={styles.fontSizeText}>{Math.round(teleprompterOpacity * 100)}</ThemedText>
                  <TouchableOpacity
                    style={styles.fontButton}
                    onPress={() => handleOpacityChange(teleprompterOpacity + 0.1)}
                  >
                    <IconSymbol name="plus" size={16} color={Colors[colorScheme ?? 'light'].text} />
                  </TouchableOpacity>
                </ThemedView>
              </ThemedView>
            </ThemedView>
          )}

          {/* 提词器文本 */}
          <View style={[styles.teleprompterContainer, { opacity: teleprompterOpacity }]}>
            <View 
              ref={textContainerRef}
              style={[
                styles.textScrollContainer,
                { transform: [{ translateY: -scrollY }] }
              ]}
            >
              <ThemedText style={[styles.teleprompterText, { fontSize }]}>
                {params.text}
              </ThemedText>
              <View style={{ height: screenHeight }} />
            </View>
          </View>

          {/* 底部控制按钮 */}
          <View style={styles.bottomControls}>
            {/* 提词器控制 */}
            <View style={styles.teleprompterControls}>
              <TouchableOpacity
                style={styles.smallButton}
                onPress={handleResetTeleprompter}
              >
                <IconSymbol name="arrow.clockwise" size={20} color="white" />
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[styles.smallButton, isPlaying && styles.activeButton]}
                onPress={handlePlayPauseTeleprompter}
              >
                <IconSymbol 
                  name={isPlaying ? "pause.fill" : "play.fill"} 
                  size={20} 
                  color="white" 
                />
              </TouchableOpacity>
            </View>

            {/* 录制控制 */}
            <TouchableOpacity
              style={[styles.recordButton, isRecording && styles.recordingButton]}
              onPress={handleStartStopRecording}
            >
              <View style={[styles.recordButtonInner, isRecording && styles.recordingInner]} />
            </TouchableOpacity>

            {/* 摄像头控制 */}
            <TouchableOpacity
              style={styles.smallButton}
              onPress={toggleCamera}
            >
              <IconSymbol name="camera.rotate" size={24} color="white" />
            </TouchableOpacity>
          </View>
        </CameraView>
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  camera: {
    flex: 1,
  },
  message: {
    textAlign: 'center',
    paddingBottom: 10,
  },
  permissionButton: {
    backgroundColor: '#2196F3',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
    alignSelf: 'center',
  },
  permissionButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  topControls: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 50,
    paddingBottom: 15,
    backgroundColor: 'rgba(0,0,0,0.3)',
  },
  topTitle: {
    color: 'white',
    fontSize: 18,
    fontWeight: '600',
    flex: 1,
    textAlign: 'center',
  },
  floatingSettings: {
    position: 'absolute',
    top: 100,
    left: 20,
    right: 20,
    backgroundColor: 'rgba(0,0,0,0.8)',
    borderRadius: 12,
    padding: 15,
  },
  settingRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  settingLabel: {
    color: 'white',
    fontSize: 14,
    fontWeight: '500',
  },
  speedControls: {
    flexDirection: 'row',
    gap: 6,
  },
  speedButton: {
    width: 30,
    height: 30,
    borderRadius: 15,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.3)',
  },
  speedButtonText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '600',
  },
  fontControls: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
  },
  opacityControls: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
  },
  fontButton: {
    width: 30,
    height: 30,
    borderRadius: 15,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.3)',
  },
  fontSizeText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '600',
    minWidth: 25,
    textAlign: 'center',
  },
  teleprompterContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 120,
    justifyContent: 'center',
    paddingHorizontal: 20,
  },
  textScrollContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: screenHeight,
  },
  teleprompterText: {
    color: 'white',
    textAlign: 'center',
    lineHeight: 1.6,
    textShadowColor: 'rgba(0,0,0,0.8)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 3,
  },
  bottomControls: {
    position: 'absolute',
    bottom: 40,
    left: 0,
    right: 0,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 40,
  },
  teleprompterControls: {
    flexDirection: 'row',
    gap: 10,
  },
  smallButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: 'rgba(255,255,255,0.2)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  activeButton: {
    backgroundColor: 'rgba(0,122,255,0.8)',
  },
  recordButton: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(255,255,255,0.2)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  recordingButton: {
    backgroundColor: 'rgba(255,59,48,0.8)',
  },
  recordButtonInner: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: 'white',
  },
  recordingInner: {
    width: 40,
    height: 40,
    borderRadius: 8,
    backgroundColor: 'white',
  },
}); 